#!/usr/bin/env python3
"""
使用本地 ultralytics_v8 的 YOLO 训练脚本
"""

import sys
import os
from pathlib import Path

# 添加本地 ultralytics_v8 目录到 Python 路径
ultralytics_path = Path(__file__).parent / "data" / "ultralytics_v8"
sys.path.insert(0, str(ultralytics_path))

# 设置环境变量
os.environ["PYTHONPATH"] = str(ultralytics_path)

def check_device():
    """检查可用的训练设备"""
    import torch
    
    if torch.cuda.is_available():
        device = "cuda:0"
        gpu_name = torch.cuda.get_device_name(0)
        print(f"🎮 检测到GPU: {gpu_name}")
        print(f"📱 使用设备: {device}")
    else:
        device = "cpu"
        print("💻 使用CPU训练")
    
    return device

def main():
    """主训练函数"""
    try:
        # 验证从本地源码导入YOLO
        import ultralytics
        print(f"✅ 使用本地ultralytics源码: {ultralytics.__file__}")
        
        from ultralytics import YOLO
        print("✅ 成功从本地源码导入YOLO")
        
        print("🚀 开始YOLO训练...")
        print(f"📁 工作目录: {Path.cwd()}")
        
        # 检查设备
        device = check_device()
        
        # 加载预训练的 YOLO11n 模型（使用本地模型文件）
        model_path = ultralytics_path / "yolov8n.pt"
        model = YOLO(str(model_path))
        print(f"📦 模型加载成功: {model_path}")
        
        # 训练模型
        print("🎯 开始训练...")
        train_results = model.train(
            data="coco8.yaml",  # 数据集配置文件路径
            epochs=100,  # 训练轮次
            imgsz=640,  # 训练图像尺寸
            device=device,  # 训练设备
            project="runs/train",  # 保存目录
            name="yolo8n_training",  # 实验名称
            verbose=True,  # 详细输出
            save=True,  # 保存模型
            plots=True,  # 保存图表
            val=True,  # 验证
            patience=10,  # 早停耐心
            cache=False,  # 不使用缓存
            workers=0,  # Windows 上设置为 0 避免多进程问题
            amp=device != "cpu",  # GPU时启用混合精度
        )
        
        print("✅ 训练完成！")
        
        # 在验证集上评估模型性能
        print("📊 开始模型评估...")
        metrics = model.val()
        print(f"📈 验证指标: {metrics}")
        
        # 对图像进行目标检测
        print("🔍 开始目标检测...")
        # 注意：这里需要确保有测试图像，如果没有可以跳过
        test_image_path = "datasets/2/images/val/example.jpg"
        if os.path.exists(test_image_path):
            results = model(test_image_path)
            results[0].show()  # 显示结果
            print("✅ 目标检测完成")
        else:
            print(f"⚠️  测试图像不存在: {test_image_path}")
        
        # 导出模型为 ONNX 格式
        print("📤 导出模型...")
        path = model.export(format="onnx")  # 返回导出模型的路径
        print(f"✅ 模型已导出到: {path}")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 ultralytics_v8 目录存在且包含完整的源码")
        
    except Exception as e:
        print(f"❌ 训练出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    import multiprocessing
    multiprocessing.freeze_support()  # ✅ 添加这行是 Windows 推荐做法
    main()