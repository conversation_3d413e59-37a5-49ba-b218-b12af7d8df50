#!/usr/bin/env python3
"""
华为NPU单机多卡YOLO训练脚本
支持单机多/单卡训练

优化特性:
1. 训练过程中保存关键性能指标，减少IO开销
2. 训练结束后获取完整模型信息并合并数据
3. 批量保存机制，提高文件操作效率
4. 智能缓存管理，避免重复保存
5. 完整的错误处理和日志记录
6. 自动生成训练总结报告

数据保存策略:
- 训练中: 每个epoch保存关键指标到内存缓存，每5个epoch批量写入文件
- 训练后: 获取模型文件信息，与缓存的指标合并，生成完整的模型记录
- 文件结构: training_module.json(模型信息), training_metrics.json(详细指标)
"""

import os
import sys
import time
import json
import logging
import psutil
import argparse
import subprocess
from pathlib import Path
import torch
from datetime import datetime


# 添加当前目录到Python路径
ultralytics_dir = "/root/siton-data-b496463103254f46976c4ff88ea74bc9/data/ultralytics_v8"
sys.path.insert(0, str(ultralytics_dir))

# 设置环境变量
os.environ["PYTHONPATH"] = str(ultralytics_dir)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def detect_device():
    """
    检测可用的训练设备，优先使用NPU
    返回: 设备标识符 ('cpu', 'cuda:0', 或 'npu:0,1,2,3')
    """
    try:
        # 首先检查NPU
        try:
            import torch_npu
            if torch_npu.npu.is_available():
                npu_count = torch_npu.npu.device_count()
                logger.info(f"发现 {npu_count} 个可用的NPU设备")

                # 根据NPU数量返回设备配置
                if npu_count > 1:
                    device = f"npu:{','.join(map(str, range(npu_count)))}"
                    print(f"🔄 自动配置: 使用所有{npu_count}个NPU - {device}")
                    return device
                else:
                    print("🔄 自动配置: 使用单个NPU - npu:0")
                    return 'npu:0'
        except ImportError:
            logger.warning("未安装torch_npu，跳过NPU检测")
            pass
        except Exception as e:
            logger.warning(f"NPU检测失败: {e}")
            pass

        # 检查GPU
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            if device_count > 0:
                gpu_name = torch.cuda.get_device_name(0)
                logger.info(f"使用GPU: {gpu_name}")
                if device_count > 1:
                    return f"cuda:{','.join(map(str, range(device_count)))}"
                else:
                    return 'cuda:0'
        logger.info("使用CPU进行训练")
        return 'cpu'
    except Exception as e:
        logger.warning(f"设备检测出错: {e}")
        return 'cpu'

def get_resource_usage():
    """
    获取系统资源使用情况
    返回: 包含CPU、内存、GPU和NPU使用率的字典
    """
    usage = {
        'cpu_usage': psutil.cpu_percent(),
        'memory_usage': psutil.virtual_memory().percent,
        'gpu_usage': 0.0,
        'npu_usage': 0.0,
        'timestamp': time.time()
    }

    # 获取GPU使用率
    try:
        if torch.cuda.is_available():
            usage['gpu_usage'] = float(torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() * 100)
    except:
        pass

    # 获取NPU使用率
    try:
        result = subprocess.check_output(['npu-smi', 'info'], universal_newlines=True)
        for line in result.split('\n'):
            if '910' in line and 'OK' in line:
                parts = line.split('|')
                if len(parts) >= 4:
                    power_info = parts[3].strip()
                    if 'W' in power_info:
                        power = float(power_info.split('W')[0].strip())
                        usage['npu_usage'] = (power / 250.0) * 100  # 假设最大功率250W
    except:
        pass

    return usage

def save_training_metrics(metrics):
    """
    保存详细的训练指标到单独的文件
    Args:
        metrics: 包含训练指标的字典
    """
    try:
        metrics_file = os.path.join(os.getcwd(), "training_metrics.json")
        with open(metrics_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(metrics, ensure_ascii=False) + '\n')
    except Exception as e:
        logger.error(f"保存训练指标失败: {e}")

def save_cached_metrics(metrics_cache, task_name):
    """
    批量保存缓存的关键性能指标
    Args:
        metrics_cache: 缓存的指标数据字典
        task_name: 任务名称
    """
    try:
        if not metrics_cache:
            return

        # 转换为模型信息格式，并进行去重检查
        metrics_list = []
        skipped_count = 0

        for epoch, metrics in metrics_cache.items():
            # 检查是否已存在相同epoch的记录
            has_model_info = 'model_path' in metrics and 'model_size_mb' in metrics

            if has_model_info:
                # 如果有模型信息，检查是否已存在完整模型记录
                if check_existing_complete_model(epoch):
                    logger.debug(f"跳过已存在的epoch {epoch}完整模型记录")
                    skipped_count += 1
                    continue
            else:
                # 如果没有模型信息，检查是否已存在指标记录
                if check_existing_metrics(epoch):
                    logger.debug(f"跳过已存在的epoch {epoch}指标记录")
                    skipped_count += 1
                    continue

            # 检查缓存中是否包含模型信息
            has_model_info = 'model_path' in metrics and 'model_size_mb' in metrics

            if has_model_info:
                # 如果有模型信息，保存完整的模型记录
                performance_metrics = {
                    'model_name': metrics.get('model_name', f"epoch{epoch}.pt"),
                    'model_path': metrics.get('model_path', ''),
                    'epoch': epoch,
                    'accuracy': metrics['accuracy'],
                    'precision': metrics['precision'],
                    'recall': metrics['recall'],
                    'box_loss': metrics['box_loss'],
                    'cls_loss': metrics['cls_loss'],
                    'dfl_loss': metrics['dfl_loss'],
                    'model_size_mb': metrics.get('model_size_mb', 0.0),
                    'inference_speed': metrics.get('inference_speed', 0.0),
                    'is_best': metrics.get('is_best', False),
                    'is_last': metrics.get('is_last', False),
                    'metrics_only': False,  # 这是完整的模型记录
                    'timestamp': metrics['timestamp']
                }
            else:
                # 如果没有模型信息，只保存指标记录
                performance_metrics = {
                    'model_name': f"epoch{epoch}.pt",
                    'epoch': epoch,
                    'accuracy': metrics['accuracy'],
                    'precision': metrics['precision'],
                    'recall': metrics['recall'],
                    'box_loss': metrics['box_loss'],
                    'cls_loss': metrics['cls_loss'],
                    'dfl_loss': metrics['dfl_loss'],
                    'metrics_only': True,  # 标记这是只有指标的记录
                    'timestamp': metrics['timestamp']
                }

            metrics_list.append(performance_metrics)

        if metrics_list:
            # 批量保存（一次性保存所有新记录）
            save_model_info_to_json(metrics_list, task_name)

            # 统计保存的记录类型
            complete_count = sum(1 for m in metrics_list if not m.get('metrics_only', True))
            metrics_count = sum(1 for m in metrics_list if m.get('metrics_only', True))

            if complete_count > 0:
                logger.info(f"✅ 批量保存了 {complete_count} 个完整模型记录")
            if metrics_count > 0:
                logger.info(f"✅ 批量保存了 {metrics_count} 个指标记录")

        if skipped_count > 0:
            logger.info(f"⏭️ 跳过了 {skipped_count} 个已存在的记录")

        # 清空缓存
        metrics_cache.clear()

    except Exception as e:
        logger.error(f"❌ 批量保存缓存指标失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

def parse_training_config(config, dataset_path=None, device=None):
    """
    解析训练配置，转换为YOLOv8可接受的参数格式
    """
    print("config", config)
    # 基础训练参数
    train_args = {
        'epochs': int(config['parameters']['epochs']), # 训练轮数
        'device': device or detect_device(), # 训练设备
        'project': 'runs_detect', # 训练结果保存路径
        'name': f'task_{config.get("id", "npu_train")}', # 训练结果保存名称
        'exist_ok': True, # 是否覆盖训练结果
        'plots': False,  # 禁用绘图以提高性能
    }

    # 数据集路径 - 优先使用命令行参数提供的路径
    if dataset_path:
        train_args['data'] = dataset_path
    else:
        # 数据集和验证参数
        dataset_dir = os.path.join('datasets', config['training']['dataset']['name']) # 数据集路径
        train_args['data'] = os.path.join(dataset_dir, 'data.yaml') # 数据集路径

    # 批次大小和学习率
    train_args['batch'] = int(config['parameters'].get('batchSize', 128)) # 批次大小
    train_args['lr0'] = float(config['parameters'].get('learningRate', 0.01)) # 学习率

    # 图像尺寸
    train_args['imgsz'] = int(config['parameters'].get('imageSize', 640)) # 图像尺寸

    # 优化器相关参数
    optimizer = config['otherParams'].get('optimizer', 'SGD').lower() # 优化器
    train_args['optimizer'] = optimizer # 优化器

    if optimizer in ['sgd', 'adam', 'adamw']:
        train_args['momentum'] = float(config['otherParams'].get('momentum', 0.937)) # 动量
        train_args['weight_decay'] = float(config['otherParams'].get('weightDecay', 0.0005)) # 权重衰减

    # 早停和检查点
    if 'earlyStopping' in config['otherParams']:
        train_args['patience'] = int(config['otherParams']['earlyStopping'])

    if 'checkpointFreq' in config['otherParams']:
        train_args['save_period'] = int(config['otherParams']['checkpointFreq'])

    # 混合精度训练
    if config['otherParams'].get('useMixedPrecision', False):
        train_args['amp'] = True  # 自动混合精度

    # 学习率策略
    lr_strategy = config['parameters'].get('learningRateStrategy', '').lower()
    if lr_strategy == '余弦衰减':
        train_args['cos_lr'] = True  # 使用余弦学习率调度

    # 标签平滑
    if 'labelSmoothing' in config['otherParams']:
        train_args['label_smoothing'] = float(config['otherParams']['labelSmoothing'])

    # Dropout
    if 'dropout' in config['otherParams']:
        train_args['dropout'] = float(config['otherParams']['dropout'])

    # 预热步数
    if 'warmupSteps' in config['otherParams']:
        warmup_epochs = int(config['otherParams']['warmupSteps']) // (train_args['batch'] * 100)  # 估算预热轮数
        train_args['warmup_epochs'] = max(1, warmup_epochs)  # 至少1轮
        train_args['warmup_momentum'] = 0.8
        train_args['warmup_bias_lr'] = 0.1

    # 数据加载器配置
    train_args['workers'] = int(config.get('resources', {}).get('workers', 8))

    # 缓存配置
    if config['otherParams'].get('useCache', False):
        train_args['cache'] = True

    return train_args

def setup_npu_environment():
    """检查NPU训练环境"""
    try:
        import torch_npu
        print(f"✅ torch_npu版本: {torch_npu.__version__}")

        # 检查NPU可用性
        if not torch_npu.npu.is_available():
            raise RuntimeError("NPU不可用，请检查驱动和torch_npu安装")

        npu_count = torch_npu.npu.device_count()
        print(f"✅ 检测到 {npu_count} 个NPU设备")

        # 显示NPU信息
        for i in range(npu_count):
            try:
                device_name = torch_npu.npu.get_device_name(i)
                device_props = torch_npu.npu.get_device_properties(i)
                memory_gb = device_props.total_memory / 1024**3
                print(f"   NPU:{i} - {device_name} ({memory_gb:.1f}GB)")
            except Exception as e:
                print(f"   NPU:{i} - 设备信息获取失败: {e}")

        return npu_count

    except ImportError:
        print("⚠️  torch_npu未安装，将使用其他可用设备")
        return 0
    except Exception as e:
        print(f"⚠️  NPU环境检查失败: {e}")
        return 0

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='华为NPU多机多卡YOLO训练')

    # 配置文件参数
    parser.add_argument('--config', type=str, default='training_config.json', help='训练配置文件路径')
    parser.add_argument('--task_id', type=str, help='任务ID，用于生成输出目录名')

    # 基本训练参数 (可覆盖配置文件)
    parser.add_argument('--model', type=str, help='模型文件路径，优先级高于配置文件')
    parser.add_argument('--data', type=str, help='数据集配置文件，优先级高于配置文件')

    return parser.parse_args()

def single_machine_train(train_args, model_path, device):
    """单机训练（单卡或多卡）"""
    from ultralytics import YOLO

    print(f"✅ 使用ultralytics版本: {YOLO.__module__}")

    print("=" * 60)
    print("🚀 启动单机NPU训练")
    print(f"📱 设备: {device}")
    print(f"📊 批次大小: {train_args['batch']}")
    print(f"🔄 训练轮次: {train_args['epochs']}")
    print("=" * 60)

    # 创建模型
    model = YOLO(model_path)

    # 获取任务名称
    task_name = train_args.get('name', 'task_default')

    # 初始化训练过程中的临时数据存储
    epoch_metrics_cache = {}

    # 添加训练回调函数
    def on_train_epoch_end(trainer):
        """每个训练周期结束时的回调函数 - 保存关键数据"""
        nonlocal task_name, epoch_metrics_cache

        try:
            # 获取资源使用情况
            resource_metrics = get_resource_usage()

            # 安全获取metrics，如果为None则使用空字典
            metrics = trainer.metrics if trainer.metrics is not None else {}

            # 从trainer.tloss获取训练损失组件
            if hasattr(trainer, 'tloss') and len(trainer.tloss) >= 3:
                # 将tensor转换为Python的float值
                box_loss = round(float(trainer.tloss[0]), 4)
                cls_loss = round(float(trainer.tloss[1]), 4)
                dfl_loss = round(float(trainer.tloss[2]), 4)
            else:
                # 如果tloss不可用，使用默认值
                box_loss = 0.0
                cls_loss = 0.0
                dfl_loss = 0.0

            # 合并训练指标和资源使用指标
            combined_metrics = {
                'epoch': trainer.epoch,
                'train/box_loss': box_loss,
                'train/cls_loss': cls_loss,
                'train/dfl_loss': dfl_loss,
                'metrics/precision': metrics.get('metrics/precision(B)', 0.0),
                'metrics/recall': metrics.get('metrics/recall(B)', 0.0),
                'metrics/mAP50': metrics.get('metrics/mAP50(B)', 0.0),
                'metrics/mAP50-95': metrics.get('metrics/mAP50-95(B)', 0.0),
                **resource_metrics
            }

            # 记录日志
            logger.info(f"Epoch {trainer.epoch} metrics: {combined_metrics}")

            # 保存详细训练指标到单独文件
            save_training_metrics(combined_metrics)

            # 获取当前epoch的模型文件信息
            model_info = None
            try:
                # 获取保存目录
                save_dir = trainer.save_dir if hasattr(trainer, 'save_dir') else 'runs_detect'
                weights_dir = os.path.join(save_dir, 'weights')

                # 查找当前epoch的模型文件
                current_epoch_model = None
                if os.path.exists(weights_dir):
                    for file in os.listdir(weights_dir):
                        if file.endswith('.pt') and f'epoch{trainer.epoch}' in file:
                            current_epoch_model = os.path.join(weights_dir, file)
                            break

                # 如果找到了当前epoch的模型文件，获取其信息
                if current_epoch_model and os.path.exists(current_epoch_model):
                    file_size = os.path.getsize(current_epoch_model)
                    model_size_mb = round(file_size / (1024 * 1024), 2)

                    # 估算推理速度
                    estimated_fps = max(10, 120 - model_size_mb * 1.5) if model_size_mb > 0 else 0.0

                    model_info = {
                        'model_name': os.path.basename(current_epoch_model),
                        'model_path': os.path.abspath(current_epoch_model),
                        'model_size_mb': model_size_mb,
                        'inference_speed': round(estimated_fps, 2),
                        'is_best': False,  # 在epoch结束时还不知道是否是最佳模型
                        'is_last': False
                    }
                    logger.debug(f"获取到Epoch {trainer.epoch}模型信息: {model_info['model_name']}, 大小: {model_size_mb}MB")
                else:
                    logger.debug(f"未找到Epoch {trainer.epoch}的模型文件")

            except Exception as e:
                logger.warning(f"获取Epoch {trainer.epoch}模型信息失败: {e}")

            # 提取并缓存关键性能指标和模型信息
            performance_metrics = {
                'epoch': trainer.epoch,
                'accuracy': round(metrics.get('metrics/mAP50(B)', 0.0), 4),
                'precision': round(metrics.get('metrics/precision(B)', 0.0), 4),
                'recall': round(metrics.get('metrics/recall(B)', 0.0), 4),
                'box_loss': box_loss,
                'cls_loss': cls_loss,
                'dfl_loss': dfl_loss,
                'timestamp': datetime.now().isoformat()
            }

            # 如果获取到了模型信息，添加到缓存中
            if model_info:
                performance_metrics.update(model_info)

            # 缓存到内存中，避免频繁文件IO
            epoch_metrics_cache[trainer.epoch] = performance_metrics

            # 每5个epoch或最后一个epoch保存一次缓存的指标
            if trainer.epoch % 5 == 0 or trainer.epoch == train_args['epochs'] - 1:
                save_cached_metrics(epoch_metrics_cache, task_name)

            logger.info(f"✅ Epoch {trainer.epoch} 关键指标和模型信息已缓存")

        except Exception as e:
            logger.error(f"❌ 保存Epoch {trainer.epoch}关键性能指标失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

    # 添加训练结束回调函数
    def on_train_end(trainer):
        """训练结束时的回调函数 - 获取其他数据并合并"""
        nonlocal task_name, epoch_metrics_cache

        logger.info("🔄 训练结束，开始处理完整的模型信息...")

        # 先保存剩余的缓存指标
        if epoch_metrics_cache:
            save_cached_metrics(epoch_metrics_cache, task_name)

        time.sleep(2)  # 给文件系统一些时间完成写入

        try:
            # 获取权重目录
            save_dir = trainer.save_dir if hasattr(trainer, 'save_dir') else 'runs_detect'
            weights_dir = os.path.join(save_dir, 'weights')

            if not os.path.exists(weights_dir):
                logger.warning(f"⚠️ 权重目录不存在: {weights_dir}")
                return

            # 获取所有模型文件
            model_files = [f for f in os.listdir(weights_dir) if f.endswith('.pt')]
            if not model_files:
                logger.warning("⚠️ 未找到任何模型文件")
                return

            logger.info(f"📁 发现 {len(model_files)} 个模型文件: {model_files}")

            # 读取已有的性能指标记录
            existing_metrics_data = load_metrics_only_data()
            logger.info(f"📊 加载了 {len(existing_metrics_data)} 个epoch的性能指标")

            # 批量处理模型文件信息
            complete_models_info = []

            for model_file in model_files:
                model_path = os.path.join(weights_dir, model_file)
                model_path = os.path.abspath(model_path)

                # 提取epoch信息
                epoch = extract_epoch_from_filename(model_file)

                # 获取模型元数据
                model_info = get_complete_model_info(model_path, model_file, epoch, existing_metrics_data)

                # 检查是否已存在完整的模型记录（按epoch检查）
                if not check_existing_complete_model(epoch):
                    complete_models_info.append(model_info)
                    logger.info(f"✅ 准备保存完整模型信息: {model_info['model_name']} (epoch {epoch})")
                else:
                    logger.info(f"⏭️ Epoch {epoch} 的完整模型信息已存在，跳过: {model_info['model_name']}")

            # 批量保存完整的模型信息
            if complete_models_info:
                batch_save_complete_models(complete_models_info, task_name)
                logger.info(f"🎉 成功保存 {len(complete_models_info)} 个完整模型信息")
            else:
                logger.info("ℹ️ 没有新的模型信息需要保存")

        except Exception as e:
            logger.error(f"❌ 处理完整模型信息失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

    # 注册回调函数
    model.add_callback('on_train_epoch_end', on_train_epoch_end)
    model.add_callback('on_train_end', on_train_end)  # 添加训练结束回调

    # 更新设备配置
    train_args['device'] = device
    train_args['verbose'] = True
    train_args['save'] = True
    train_args['plots'] = True
    train_args['val'] = True
    train_args['save_period'] = 1  # 每轮保存一次

    print("🎯 开始训练...")
    try:
        logger.info("🚀 启动训练流程...")
        results = model.train(**train_args)

        # 训练结束后手动调用on_train_end函数，确保获取所有模型信息
        logger.info("🔄 训练完成，手动调用on_train_end处理模型信息...")
        on_train_end(model.trainer)

        logger.info("✅ 训练流程全部完成！")
        print(f"📈 结果保存在: {model.trainer.save_dir}")
        print(f"📊 模型信息保存在: training_module.json")
        print(f"📋 训练指标保存在: training_metrics.json")

        return results
    except Exception as e:
        logger.error(f"❌ 训练失败: {e}")
        # 即使训练失败，也尝试保存已有的数据
        try:
            if epoch_metrics_cache:
                save_cached_metrics(epoch_metrics_cache, task_name)
                logger.info("💾 已保存训练失败前的指标数据")
        except:
            pass
        raise

def get_model_info(model_path, metrics):
    """
    获取模型信息
    Args:
        model_path: 模型文件路径
        metrics: 训练指标
    Returns:
        dict: 模型信息
    """
    # 确保model_path是绝对路径
    model_path = os.path.abspath(model_path)
    
    model_info = {
        'model_name': os.path.basename(model_path),
        'model_path': model_path,
        'accuracy': 0.0,
        'precision': 0.0,
        'recall': 0.0,
        'inference_speed': 0.0,
        'model_size_mb': 0.0,
        'timestamp': datetime.now().isoformat()
    }
    
    try:
        # 获取模型大小
        if os.path.exists(model_path):
            file_size = os.path.getsize(model_path)
            model_info['model_size_mb'] = round(file_size / (1024 * 1024), 2)
            logger.info(f"模型大小: {model_info['model_size_mb']} MB")
        else:
            logger.warning(f"模型文件不存在: {model_path}")
        
        # 从metrics中获取准确率、精度、召回率
        model_info['accuracy'] = round(metrics.get('metrics/mAP50(B)', 0.0), 4)
        model_info['precision'] = round(metrics.get('metrics/precision(B)', 0.0), 4)
        model_info['recall'] = round(metrics.get('metrics/recall(B)', 0.0), 4)
        
        # 计算推理速度（这里使用一个估算值，实际应该通过推理测试获得）
        model_size_mb = model_info['model_size_mb']
        if model_size_mb > 0:
            # 简单的推理速度估算（FPS），实际应该通过真实推理测试
            estimated_fps = max(10, 100 - model_size_mb * 2)  # 模型越大，FPS越低
            model_info['inference_speed'] = round(estimated_fps, 2)
        
        logger.info(f"获取模型信息成功: {model_info}")
        
    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    return model_info

def save_model_info_to_json(model_info, task_name=None):
    """
    保存模型信息到training_module.json文件
    支持单个模型信息或模型信息列表
    
    Args:
        model_info: 单个模型信息字典或模型信息列表
        task_name: 任务名称（可选）
    """
    # 统一处理单个和多个模型信息
    if isinstance(model_info, dict):
        models_info = [model_info]
    elif isinstance(model_info, list):
        models_info = model_info
    else:
        logger.error("model_info必须是字典或列表")
        return
    
    if not models_info:
        logger.warning("没有模型信息需要保存")
        return

    # 构建文件路径
    json_file_path = os.path.join(os.getcwd(), "training_module.json")
    existing_data = []
    
    try:
        # 读取现有数据
        if os.path.exists(json_file_path):
            with open(json_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    existing_data = json.loads(content)
                    logger.debug(f"读取了{len(existing_data)}条现有记录")
    
        # 处理每个模型信息
        updated = False
        for info in models_info:
            model_name = info.get('model_name')
            metrics_only = info.get('metrics_only', False)
            epoch = info.get('epoch')

            # 检查是否需要跳过重复记录
            should_skip = False
            existing_idx = None

            # 查找匹配的现有记录
            for i, item in enumerate(existing_data):
                # 对于metrics_only记录，按epoch匹配
                if metrics_only and item.get('epoch') == epoch and item.get('metrics_only', False):
                    existing_idx = i
                    break
                # 对于完整模型记录，也按epoch匹配（同一epoch只保留一个完整记录）
                elif not metrics_only and item.get('epoch') == epoch and not item.get('metrics_only', False):
                    # 如果是相同的model_name，则更新；如果是不同的model_name，则跳过
                    if item.get('model_name') == model_name:
                        existing_idx = i
                    else:
                        logger.info(f"跳过重复epoch {epoch}的记录: {model_name} (已存在: {item.get('model_name')})")
                        should_skip = True
                    break

            # 跳过重复记录
            if should_skip:
                continue

            # 更新或添加记录
            if existing_idx is not None:
                existing_data[existing_idx] = info
                logger.debug(f"更新记录: {model_name}")
                updated = True
            else:
                existing_data.append(info)
                logger.debug(f"添加新记录: {model_name}")
                updated = True
                
        # 保存到文件
        if updated:
            with open(json_file_path + '.tmp', 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, indent=2, ensure_ascii=False)
                f.flush()
                os.fsync(f.fileno())
            
            os.replace(json_file_path + '.tmp', json_file_path)
            logger.info(f"已保存{len(models_info)}条模型信息记录")
    except Exception as e:
        logger.error(f"保存模型信息失败: {e}")
        import traceback
        logger.error(traceback.format_exc())



def check_existing_metrics(epoch):
    """
    检查是否存在指定epoch的指标记录
    Args:
        epoch: 训练轮次
    Returns:
        bool: 如果存在返回True，否则返回False
    """
    try:
        # 构建文件路径
        current_dir = os.getcwd()
        json_file_path = os.path.join(current_dir, "training_module.json")
        
        # 如果文件不存在，返回False
        if not os.path.exists(json_file_path):
            return False
        
        # 读取现有数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if not content.strip():  # 文件为空
                return False
                
            existing_data = json.loads(content)
            
            # 检查是否存在相同epoch且为metrics_only的记录
            for item in existing_data:
                if item.get('epoch') == epoch and item.get('metrics_only', False):
                    return True
            
            return False
            
    except Exception as e:
        logger.error(f"检查指标记录是否存在时出错: {e}")
        return False  # 出错时默认返回False，允许保存

def check_existing_complete_model(epoch):
    """
    检查是否存在指定epoch的完整记录（非metrics_only）
    Args:
        epoch: 训练轮次
    Returns:
        bool: 如果存在返回True，否则返回False
    """
    try:
        # 构建文件路径
        current_dir = os.getcwd()
        json_file_path = os.path.join(current_dir, "training_module.json")

        # 如果文件不存在，返回False
        if not os.path.exists(json_file_path):
            return False

        # 读取现有数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if not content.strip():  # 文件为空
                return False

            existing_data = json.loads(content)

            # 检查是否存在相同epoch的完整模型记录且不是metrics_only
            for item in existing_data:
                if item.get('epoch') == epoch and not item.get('metrics_only', False):
                    return True

            return False

    except Exception as e:
        logger.error(f"检查完整模型是否存在时出错: {e}")
        return False  # 出错时默认返回False，允许保存

def load_metrics_only_data():
    """
    加载所有已保存的性能指标记录，按epoch索引
    Returns:
        dict: 以epoch为键，指标数据为值的字典
    """
    metrics_data = {}
    try:
        # 构建文件路径
        current_dir = os.getcwd()
        json_file_path = os.path.join(current_dir, "training_module.json")

        # 如果文件不存在，返回空字典
        if not os.path.exists(json_file_path):
            return metrics_data

        # 读取现有数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if not content.strip():  # 文件为空
                return metrics_data

            existing_data = json.loads(content)

            # 提取所有metrics_only记录
            for item in existing_data:
                if item.get('metrics_only', False) and 'epoch' in item:
                    epoch = item['epoch']
                    metrics_data[epoch] = {
                        'accuracy': item.get('accuracy', 0.0),
                        'precision': item.get('precision', 0.0),
                        'recall': item.get('recall', 0.0)
                    }

    except Exception as e:
        logger.error(f"加载指标数据失败: {e}")

    return metrics_data

def extract_epoch_from_filename(filename):
    """
    从文件名中提取epoch信息
    Args:
        filename: 模型文件名
    Returns:
        int or None: epoch数字，如果无法提取则返回None
    """
    try:
        # 处理不同的文件名格式
        if 'best' in filename:
            return 'best'
        elif 'last' in filename:
            return 'last'
        elif 'epoch' in filename:
            # 尝试多种格式: task_1_epoch10.pt, epoch10.pt, model_epoch_10.pt
            parts = filename.replace('.pt', '').split('_')
            for part in parts:
                if part.startswith('epoch'):
                    epoch_str = part.replace('epoch', '')
                    if epoch_str.isdigit():
                        return int(epoch_str)

        # 如果没有明确的epoch标识，尝试从数字中推断
        import re
        numbers = re.findall(r'\d+', filename)
        if numbers:
            # 假设最后一个数字是epoch
            return int(numbers[-1])

    except Exception as e:
        logger.debug(f"无法从文件名 {filename} 提取epoch: {e}")

    return None

def get_complete_model_info(model_path, model_file, epoch, existing_metrics_data):
    """
    获取完整的模型信息，包括文件信息和性能指标
    Args:
        model_path: 模型文件完整路径
        model_file: 模型文件名
        epoch: epoch信息
        existing_metrics_data: 已有的性能指标数据
    Returns:
        dict: 完整的模型信息
    """
    model_info = {
        'model_name': model_file,
        'model_path': model_path,
        'epoch': epoch,
        'is_best': 'best' in model_file,
        'is_last': 'last' in model_file,
        'inference_speed': 0.0,
        'model_size_mb': 0.0,
        'accuracy': 0.0,
        'precision': 0.0,
        'recall': 0.0,
        'timestamp': datetime.now().isoformat()
    }

    try:
        # 获取模型文件大小
        if os.path.exists(model_path):
            file_size = os.path.getsize(model_path)
            model_info['model_size_mb'] = round(file_size / (1024 * 1024), 2)

        # 估算推理速度（基于模型大小的简单估算）
        model_size_mb = model_info['model_size_mb']
        if model_size_mb > 0:
            # 简单的推理速度估算公式
            estimated_fps = max(10, 120 - model_size_mb * 1.5)
            model_info['inference_speed'] = round(estimated_fps, 2)

        # 合并性能指标
        if epoch in existing_metrics_data:
            # 使用训练过程中保存的准确指标
            metrics = existing_metrics_data[epoch]
            model_info['accuracy'] = metrics.get('accuracy', 0.0)
            model_info['precision'] = metrics.get('precision', 0.0)
            model_info['recall'] = metrics.get('recall', 0.0)
            logger.debug(f"使用缓存的性能指标 epoch {epoch}")
        else:
            logger.warning(f"未找到 epoch {epoch} 的性能指标，使用默认值")

    except Exception as e:
        logger.error(f"获取模型信息失败 {model_file}: {e}")

    return model_info

def batch_save_complete_models(models_info_list, task_name):
    """
    批量保存完整的模型信息，带去重检查
    Args:
        models_info_list: 模型信息列表
        task_name: 任务名称
    """
    try:
        if not models_info_list:
            return

        # 进行去重检查
        unique_models = []
        skipped_count = 0

        for model_info in models_info_list:
            model_name = model_info.get('model_name')
            epoch = model_info.get('epoch')

            # 检查是否已存在完整的模型记录（按epoch检查）
            if check_existing_complete_model(epoch):
                logger.debug(f"跳过已存在的完整模型记录: {model_name} (epoch {epoch})")
                skipped_count += 1
                continue

            unique_models.append(model_info)

        if unique_models:
            # 批量保存（一次性保存所有新模型）
            save_model_info_to_json(unique_models, task_name)
            logger.info(f"✅ 批量保存完成，共 {len(unique_models)} 个新模型")

        if skipped_count > 0:
            logger.info(f"⏭️ 跳过了 {skipped_count} 个已存在的模型记录")

    except Exception as e:
        logger.error(f"❌ 批量保存模型信息失败: {e}")
        import traceback
        logger.error(traceback.format_exc())



def main():
    """主函数"""
    print("🚀 华为NPU单机多卡YOLO训练")
    print(f"📁 工作目录: {Path(__file__).parent.absolute()}")
    
    # 显示ultralytics版本信息
    try:
        from ultralytics import YOLO
        print(f"\n✅ 使用ultralytics版本: {YOLO.__module__}")
    except ImportError as e:
        print(f"\n❌ ultralytics导入失败: {e}")
        print("请确保ultralytics_v8目录存在且包含完整的源码")
        sys.exit(1)

    args = parse_arguments()

    # 读取配置文件
    try:
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"✅ 成功加载配置文件: {args.config}")
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {args.config}")
        print("💡 请确保配置文件存在，或使用 --config 参数指定正确路径")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        sys.exit(1)
    
    try:
        
        # 检查NPU环境
        print("\n" + "="*50)
        print("🔧 检查NPU环境")
        print("="*50)
        setup_npu_environment()
        
        # 配置设备
        print("\n" + "="*50)
        print("⚙️ 配置训练设备")
        print("="*50)
        device = detect_device()  # 自动检测设备

        # 解析训练配置
        train_args = parse_training_config(config, args.data, device)

        # 命令行参数覆盖配置文件参数
        if args.model:  
            model_path = args.model
        else:
            model_path = config.get('model', {}).get('path', 'yolov8n.pt')

        # 如果提供了task_id，更新name
        if args.task_id:
            train_args['name'] = f'task_{args.task_id}'

        print(f"📋 训练配置:")
        print(f"   模型: {model_path}")
        print(f"   数据集: {train_args['data']}")
        print(f"   轮次: {train_args['epochs']}")
        print(f"   批次大小: {train_args['batch']}")
        print(f"   设备: {device}")

        # 启动单机训练
        training_results = single_machine_train(train_args, model_path, device)

        print("✅ 所有训练任务完成！")

        # 显示训练结果摘要
        if training_results:
            print(f"📊 训练结果: {type(training_results).__name__}")
        else:
            print("⚠️ 训练结果为空")
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()