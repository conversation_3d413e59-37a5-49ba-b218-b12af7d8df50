# Generated by Django 4.2.7 on 2025-08-06 03:40

import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON>ield(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('mobile', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('real_name', models.CharField(blank=True, max_length=50, null=True, verbose_name='真实姓名')),
                ('company', models.CharField(blank=True, max_length=100, null=True, verbose_name='公司')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='电话')),
                ('gender', models.CharField(choices=[('male', '男'), ('female', '女'), ('secret', '保密')], default='secret', max_length=10, verbose_name='性别')),
                ('department', models.CharField(blank=True, max_length=100, null=True, verbose_name='所属部门')),
                ('work_department', models.CharField(blank=True, max_length=100, null=True, verbose_name='工作部门')),
                ('position', models.CharField(blank=True, max_length=100, null=True, verbose_name='用户岗位')),
                ('description', models.TextField(blank=True, null=True, verbose_name='用户简述')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='user_avatar/', verbose_name='用户头像')),
                ('certificate_serial_number', models.CharField(blank=True, help_text='用户证书序列号', max_length=100, null=True)),
                ('certificate_content', models.TextField(blank=True, help_text='用户证书内容(PEM格式)', null=True)),
                ('certificate_issued_at', models.DateTimeField(blank=True, help_text='证书颁发时间', null=True)),
                ('certificate_expires_at', models.DateTimeField(blank=True, help_text='证书过期时间', null=True)),
                ('certificate_revoked', models.BooleanField(default=False, help_text='证书是否已撤销')),
                ('certificate_revoked_at', models.DateTimeField(blank=True, help_text='证书撤销时间', null=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'db_table': 'user_user',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
