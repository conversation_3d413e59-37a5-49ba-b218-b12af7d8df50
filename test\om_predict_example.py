#!/usr/bin/env python3
"""
OM模型推理示例脚本
演示如何使用修改后的om_infer.py进行图片推理
"""

import os
import subprocess
from pathlib import Path

def run_om_predict():
    """运行OM模型推理示例"""
    
    # 示例参数
    examples = [
        {
            "name": "单张图片推理",
            "cmd": [
                "python", "test/om_infer.py",
                "--mode", "predict",
                "--weight", "./weights/yolov8n.pt",
                "--om", "./weights/yolov8n_bs1.om", 
                "--source", "data/test.jpg",
                "--save_dir", "runs/predict/single_image",
                "--conf", "0.5",
                "--iou", "0.45",
                "--save_img",
                "--save_json"
            ]
        },
        {
            "name": "批量图片推理",
            "cmd": [
                "python", "test/om_infer.py",
                "--mode", "predict",
                "--weight", "./weights/yolov8n.pt",
                "--om", "./weights/yolov8n_bs1.om",
                "--source", "data/images/",
                "--save_dir", "runs/predict/batch_images",
                "--conf", "0.3",
                "--save_img",
                "--save_json",
                "--save_crop"
            ]
        },
        {
            "name": "通配符模式推理",
            "cmd": [
                "python", "test/om_infer.py",
                "--mode", "predict",
                "--weight", "./weights/yolov8n.pt",
                "--om", "./weights/yolov8n_bs1.om",
                "--source", "data/test_images/*.jpg",
                "--save_dir", "runs/predict/wildcard",
                "--conf", "0.6",
                "--save_img"
            ]
        }
    ]
    
    print("🚀 OM模型推理示例")
    print("=" * 60)
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['name']}")
        print("-" * 40)
        print("命令:")
        print(" ".join(example['cmd']))
        
        # 询问是否运行
        response = input(f"\n是否运行此示例? (y/n): ").lower()
        if response == 'y':
            try:
                print("正在运行...")
                result = subprocess.run(example['cmd'], capture_output=True, text=True)
                if result.returncode == 0:
                    print("✅ 运行成功!")
                    print("输出:", result.stdout[-200:] if len(result.stdout) > 200 else result.stdout)
                else:
                    print("❌ 运行失败!")
                    print("错误:", result.stderr)
            except Exception as e:
                print(f"❌ 执行错误: {e}")
        else:
            print("跳过此示例")

def show_usage():
    """显示使用说明"""
    print("""
🔧 OM模型推理使用说明

1. 基本用法:
   python test/om_infer.py --mode predict --weight model.pt --om model.om --source image.jpg

2. 参数说明:
   --mode predict          # 推理模式
   --weight model.pt       # PT模型路径（用于获取类别名称）
   --om model.om          # OM模型路径
   --source               # 输入源（图片/目录/通配符）
   --save_dir             # 输出目录
   --conf 0.5             # 置信度阈值
   --iou 0.45             # NMS IoU阈值
   --save_img             # 保存标注图片
   --save_json            # 保存JSON结果
   --save_crop            # 保存裁剪框

3. 输入源格式:
   - 单张图片: data/test.jpg
   - 图片目录: data/images/
   - 通配符: data/*.jpg

4. 输出文件:
   - 标注图片: {save_dir}/{image_name}_annotated.jpg
   - JSON结果: {save_dir}/{image_name}.json
   - 裁剪框: {save_dir}/crops/{class_name}/{image_name}_{box_id}.jpg

5. JSON结果格式:
   [
     {
       "bbox": [x1, y1, x2, y2],
       "confidence": 0.85,
       "class": 0,
       "class_name": "person"
     }
   ]

6. 类似yolo predict的用法:
   # 原始YOLO命令
   yolo predict model=weights/best.pt source='data/test.jpg' save conf=0.5
   
   # 对应的OM推理命令
   python test/om_infer.py --mode predict --weight weights/best.pt --om weights/best.om --source data/test.jpg --save_img --conf 0.5
""")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        show_usage()
    else:
        print("OM模型推理工具")
        print("使用 --help 查看详细说明")
        print()
        
        choice = input("选择操作: [1] 运行示例 [2] 查看使用说明 [q] 退出: ").lower()
        
        if choice == '1':
            run_om_predict()
        elif choice == '2':
            show_usage()
        elif choice == 'q':
            print("退出")
        else:
            print("无效选择")
