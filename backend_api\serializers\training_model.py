"""
训练模型相关的序列化器
"""

from rest_framework import serializers
from backend_api.models.training_model import TrainingModel, ModelConversionLog, ModelInferenceLog


class TrainingModelSerializer(serializers.ModelSerializer):
    """训练模型序列化器"""
    
    class Meta:
        model = TrainingModel
        fields = [
            'id', 'task', 'model_name', 'model_path', 
            'accuracy', 'precision', 'recall', 
            'inference_speed', 'model_size_mb',
            'is_best', 'epoch', 'is_converted',
            'created_time', 'updated_time'
        ]
        read_only_fields = ['id', 'created_time', 'updated_time']
        
class TrainingModelDetailSerializer(serializers.ModelSerializer):
    """训练模型详细信息序列化器"""
    
    class Meta:
        model = TrainingModel
        fields = [
            'id', 'task', 'model_name', 'model_path', 
            'accuracy', 'precision', 'recall', 
            'inference_speed', 'model_size_mb',
            'is_best', 'epoch',
            'server_ip', 'server_port',  # 不包含密码字段，保护敏感信息
            'created_time', 'updated_time'
        ]
        read_only_fields = ['id', 'created_time', 'updated_time']


class TrainingModelCreateSerializer(serializers.ModelSerializer):
    """创建训练模型的序列化器"""

    class Meta:
        model = TrainingModel
        fields = [
            'task', 'model_name', 'model_path',
            'accuracy', 'precision', 'recall',
            'inference_speed', 'inference_time_ms', 'model_size_mb',
            'num_classes', 'architecture', 'fitness',
            'notes'
        ]

    def validate_model_path(self, value):
        """验证模型路径"""
        if value and not value.endswith('.pt'):
            raise serializers.ValidationError("模型文件必须是.pt格式")
        return value


class TrainingModelUpdateSerializer(serializers.ModelSerializer):
    """更新训练模型的序列化器"""
    
    class Meta:
        model = TrainingModel
        fields = [
            'accuracy', 'precision', 'recall',
            'inference_speed', 'model_size_mb',
            'is_best', 'epoch', 'is_converted',
            'conversion_status', 'converted_model_path',
            'server_ip', 'server_port', 'server_password'
        ]


class ModelConversionLogSerializer(serializers.ModelSerializer):
    """模型转换日志序列化器"""
    
    model_name = serializers.CharField(source='training_model.model_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    # 格式化时间字段
    start_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    end_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    # 计算转换耗时
    duration_seconds = serializers.SerializerMethodField()
    
    class Meta:
        model = ModelConversionLog
        fields = [
            'id', 'training_model', 'model_name',
            'status', 'start_time', 'end_time', 'duration_seconds',
            'conversion_format', 'conversion_device',
            'output_path', 'file_size_mb',
            'error_message', 'log_content',
            'created_by', 'created_by_name'
        ]
        read_only_fields = ['id', 'start_time', 'end_time']
    
    def get_duration_seconds(self, obj):
        """计算转换耗时"""
        if obj.start_time and obj.end_time:
            delta = obj.end_time - obj.start_time
            return round(delta.total_seconds(), 2)
        return None


class ModelInferenceLogSerializer(serializers.ModelSerializer):
    """模型推理日志序列化器"""
    
    model_name = serializers.CharField(source='training_model.model_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    # 格式化时间字段
    created_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    class Meta:
        model = ModelInferenceLog
        fields = [
            'id', 'training_model', 'model_name',
            'input_source', 'inference_time_ms', 'confidence_threshold',
            'detections_count', 'result_data', 'output_path',
            'created_by', 'created_by_name', 'created_time'
        ]
        read_only_fields = ['id', 'created_time']


class ModelConversionRequestSerializer(serializers.Serializer):
    """模型转换请求序列化器"""

    model_id = serializers.IntegerField(help_text="要转换的模型ID")
    conversion_format = serializers.ChoiceField(
        choices=['om', 'onnx', 'tensorrt'],
        default='om',
        required=False,
        help_text="转换格式"
    )
    chip_name = serializers.CharField(
        default="910B3",
        required=False,
        help_text="芯片型号，如910B3、310P等"
    )
    
    def validate_model_id(self, value):
        """验证模型ID是否存在"""
        if value is not None:
            try:
                TrainingModel.objects.get(id=value)
            except TrainingModel.DoesNotExist:
                raise serializers.ValidationError("指定的模型不存在")
        return value


class ModelInferenceRequestSerializer(serializers.Serializer):
    """模型推理请求序列化器"""

    model_id = serializers.IntegerField(help_text="要使用的模型ID")
    input_source = serializers.CharField(required=False, allow_blank=True, help_text="输入源：图片路径、URL或base64数据")
    confidence_threshold = serializers.FloatField(
        default=0.5,
        min_value=0.0,
        max_value=1.0,
        required=False,
        help_text="置信度阈值"
    )
    iou_threshold = serializers.FloatField(
        default=0.45,
        min_value=0.0,
        max_value=1.0,
        required=False,
        help_text="IoU阈值"
    )
    max_detections = serializers.IntegerField(
        default=1000,
        min_value=1,
        max_value=10000,
        required=False,
        help_text="最大检测数量"
    )
    save_result = serializers.BooleanField(
        default=True,
        required=False,
        help_text="是否保存推理结果"
    )
    return_image = serializers.BooleanField(
        default=False,
        required=False,
        help_text="是否返回标注后的图片"
    )

    def validate_model_id(self, value):
        """验证模型ID是否存在"""
        if value is not None:
            try:
                TrainingModel.objects.get(id=value)
            except TrainingModel.DoesNotExist:
                raise serializers.ValidationError("指定的模型不存在")
        return value


class ModelListFilterSerializer(serializers.Serializer):
    """模型列表过滤序列化器"""
    
    task_id = serializers.IntegerField(required=False, help_text="按任务ID过滤")
    is_converted = serializers.BooleanField(required=False, help_text="按转换状态过滤")
    architecture = serializers.CharField(required=False, help_text="按模型架构过滤")
    min_accuracy = serializers.FloatField(required=False, help_text="最小准确率")
    max_model_size = serializers.FloatField(required=False, help_text="最大模型大小(MB)")
    search = serializers.CharField(required=False, help_text="搜索关键词")
    page = serializers.IntegerField(default=1, min_value=1, help_text="页码")
    page_size = serializers.IntegerField(default=20, min_value=1, max_value=100, help_text="每页数量")
