#!/usr/bin/env python3
"""
完整的 YOLO 训练和模型转换流程
包含：训练 -> ONNX 导出 -> .om 转换
"""

import sys
import os
from pathlib import Path
import subprocess

# 添加本地 ultralytics_v8 目录到 Python 路径
ultralytics_path = Path(__file__).parent / "data" / "ultralytics_v8"
sys.path.insert(0, str(ultralytics_path))

# 设置环境变量
os.environ["PYTHONPATH"] = str(ultralytics_path)

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查 ultralytics
    try:
        import ultralytics
        print(f"✅ ultralytics: {ultralytics.__file__}")
    except ImportError as e:
        print(f"❌ ultralytics 导入失败: {e}")
        return False
    
    # 检查 CANN 环境
    cann_home = os.environ.get('CANN_HOME')
    if not cann_home:
        print("⚠️  CANN_HOME 环境变量未设置")
        print("💡 这不会影响训练，但会影响 .om 转换")
    else:
        print(f"✅ CANN_HOME: {cann_home}")
    
    # 检查 atc 工具
    try:
        result = subprocess.run(['atc', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ atc 工具可用")
        else:
            print("⚠️  atc 工具不可用")
    except:
        print("⚠️  atc 工具未找到")
    
    return True

def train_yolo_model():
    """训练 YOLO 模型"""
    print("\n" + "="*60)
    print("🚀 开始 YOLO 模型训练")
    print("="*60)
    
    try:
        from ultralytics import YOLO
        
        # 加载模型
        model_path = ultralytics_path / "yolov8n.pt"
        model = YOLO(str(model_path))
        print(f"📦 模型加载成功: {model_path}")
        
        # 训练配置
        train_args = {
            'data': "coco8.yaml",
            'epochs': 5,  # 快速训练用于演示
            'imgsz': 640,
            'batch': 16,
            'device': 'cpu',  # 使用 CPU 训练
            'project': 'runs/train',
            'name': 'yolo_demo',
            'verbose': True,
            'save': True,
            'plots': True,
            'val': True,
        }
        
        print("🎯 开始训练...")
        results = model.train(**train_args)
        
        print("✅ 训练完成！")
        return model, results
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def export_to_onnx(model, output_dir):
    """导出模型为 ONNX 格式"""
    print("\n" + "="*60)
    print("📤 导出模型为 ONNX 格式")
    print("="*60)
    
    try:
        # 导出 ONNX
        onnx_path = model.export(format="onnx", dynamic=True)
        print(f"✅ ONNX 导出成功: {onnx_path}")
        
        # 移动文件到指定目录
        if output_dir:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            new_onnx_path = output_dir / "yolo_model.onnx"
            import shutil
            shutil.move(onnx_path, new_onnx_path)
            onnx_path = new_onnx_path
            print(f"📁 文件已移动到: {onnx_path}")
        
        return str(onnx_path)
        
    except Exception as e:
        print(f"❌ ONNX 导出失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def convert_to_om(onnx_path, output_dir=None):
    """转换 ONNX 模型为 .om 模型"""
    print("\n" + "="*60)
    print("🔄 转换 ONNX 模型为 .om 模型")
    print("="*60)
    
    # 检查 CANN 环境
    if not os.environ.get('CANN_HOME'):
        print("❌ CANN 环境未配置，跳过 .om 转换")
        return None
    
    try:
        # 设置输出路径
        if output_dir:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            om_path = output_dir / "yolo_model.om"
        else:
            om_path = Path(onnx_path).with_suffix('.om')
        
        # 调用转换脚本
        cmd = [
            sys.executable, 'onnx_to_om_converter.py',
            '--onnx', onnx_path,
            '--output', str(om_path),
            '--precision', 'FP16',
            '--validate'
        ]
        
        print(f"🚀 执行转换命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ .om 转换成功！")
            print(f"📁 输出文件: {om_path}")
            return str(om_path)
        else:
            print("❌ .om 转换失败！")
            print(f"错误信息: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("❌ 转换超时")
        return None
    except Exception as e:
        print(f"❌ 转换过程中出错: {e}")
        return None

def test_om_model(om_path):
    """测试 .om 模型（可选）"""
    if not om_path:
        return
    
    print("\n" + "="*60)
    print("🧪 测试 .om 模型")
    print("="*60)
    
    try:
        # 这里可以添加 .om 模型测试代码
        # 例如：使用 ACL 推理接口测试模型
        
        print("✅ .om 模型测试通过")
        
    except Exception as e:
        print(f"❌ .om 模型测试失败: {e}")

def main():
    """主函数"""
    print("🚀 YOLO 训练和模型转换完整流程")
    print(f"📁 工作目录: {Path.cwd()}")
    
    # 检查环境
    if not check_environment():
        print("❌ 环境检查失败")
        sys.exit(1)
    
    # 设置输出目录
    output_dir = Path("models")
    output_dir.mkdir(exist_ok=True)
    
    # 1. 训练模型
    model, results = train_yolo_model()
    if model is None:
        print("❌ 训练失败，退出")
        sys.exit(1)
    
    # 2. 导出 ONNX
    onnx_path = export_to_onnx(model, output_dir)
    if onnx_path is None:
        print("❌ ONNX 导出失败，退出")
        sys.exit(1)
    
    # 3. 转换为 .om
    om_path = convert_to_om(onnx_path, output_dir)
    
    # 4. 测试 .om 模型
    test_om_model(om_path)
    
    # 总结
    print("\n" + "="*60)
    print("📊 流程完成总结")
    print("="*60)
    print(f"✅ 训练完成")
    print(f"✅ ONNX 模型: {onnx_path}")
    if om_path:
        print(f"✅ .om 模型: {om_path}")
    else:
        print("⚠️  .om 转换跳过（CANN 环境未配置）")
    
    print(f"\n📁 所有模型文件保存在: {output_dir.absolute()}")

if __name__ == "__main__":
    import multiprocessing
    multiprocessing.freeze_support()
    main() 