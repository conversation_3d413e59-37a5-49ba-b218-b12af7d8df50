# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

import os
import shutil
import socket
import sys
import tempfile

from . import USER_CONFIG_DIR
from .torch_utils import TORCH_1_9


def find_free_network_port() -> int:
    """
    Find a free port on localhost.

    It is useful in single-node training when we don't want to connect to a real main node but have to set the
    `MASTER_PORT` environment variable.

    Returns:
        (int): The available network port number.
    """
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(("127.0.0.1", 0))
        return s.getsockname()[1]  # port


def generate_ddp_file(trainer):
    """Generates a DDP file and returns its file name."""
    module, name = f"{trainer.__class__.__module__}.{trainer.__class__.__name__}".rsplit(".", 1)

    content = f"""
# Ultralytics Multi-GPU training temp file (should be automatically deleted after use)
overrides = {vars(trainer.args)}

if __name__ == "__main__":
    from {module} import {name}
    from ultralytics.utils import DEFAULT_CFG_DICT

    cfg = DEFAULT_CFG_DICT.copy()
    cfg.update(save_dir='')   # handle the extra key 'save_dir'
    trainer = {name}(cfg=cfg, overrides=overrides)
    trainer.args.model = "{getattr(trainer.hub_session, "model_url", trainer.args.model)}"
    results = trainer.train()
"""
    (USER_CONFIG_DIR / "DDP").mkdir(exist_ok=True)
    with tempfile.NamedTemporaryFile(
        prefix="_temp_",
        suffix=f"{id(trainer)}.py",
        mode="w+",
        encoding="utf-8",
        dir=USER_CONFIG_DIR / "DDP",
        delete=False,
    ) as file:
        file.write(content)
    return file.name


def generate_ddp_command(world_size, trainer):
    """
    Generate command for distributed training.

    Args:
        world_size (int): Number of processes to spawn for distributed training.
        trainer (object): The trainer object containing configuration for distributed training.

    Returns:
        cmd (List[str]): The command to execute for distributed training.
        file (str): Path to the temporary file created for DDP training.
    """
    import __main__  # noqa local import to avoid https://github.com/Lightning-AI/lightning/issues/15218

    if not trainer.resume:
        shutil.rmtree(trainer.save_dir)  # remove the save_dir
    file = generate_ddp_file(trainer)
    dist_cmd = "torch.distributed.run" if TORCH_1_9 else "torch.distributed.launch"
    port = find_free_network_port()
    
    # Check if we need to source Ascend environment for NPU training
    ascend_env_path = "/usr/local/Ascend/ascend-toolkit/set_env.sh"
    python_cmd = f"{sys.executable} -m {dist_cmd} --nproc_per_node {world_size} --master_port {port} {file}"
    
    # Setup NPU environment variables according to Huawei official guidelines
    npu_env_vars = _get_npu_environment_setup()
    
    if os.path.exists(ascend_env_path):
        # Use bash to source Ascend environment and set NPU variables then run the distributed command
        env_setup = " && ".join([f"export {k}='{v}'" for k, v in npu_env_vars.items()])
        full_cmd = f"source {ascend_env_path} && {env_setup} && {python_cmd}"
        cmd = ["bash", "-c", full_cmd]
    else:
        # Fallback: set environment variables and run original command structure
        for k, v in npu_env_vars.items():
            os.environ[k] = v
    cmd = [sys.executable, "-m", dist_cmd, "--nproc_per_node", f"{world_size}", "--master_port", f"{port}", file]
    
    return cmd, file


def _get_npu_environment_setup():
    """
    Get NPU environment variables setup according to Huawei official guidelines.
    
    Returns:
        dict: Dictionary of environment variables to set for NPU training.
    """
    npu_env = {
        # 精度模式设置（华为官方推荐的解决方案）
        'ACL_PRECISION_MODE': 'allow_fp32_to_fp16',
        
        # HCCL相关设置
        'HCCL_WHITELIST_DISABLE': '1',      # 禁用HCCL设备白名单检查
        'HCCL_CONNECT_TIMEOUT': '600',      # 增加HCCL超时时间
        'HCCL_BLOCKING_WAIT': '1',          # 设置阻塞等待
        
        # 日志设置（便于问题排查）
        'ASCEND_GLOBAL_LOG_LEVEL': '1',     # INFO级别日志
        'ASCEND_SLOG_PRINT_TO_STDOUT': '1', # 打印日志到控制台
        
        # 其他优化设置
        'PYTHONUNBUFFERED': '1',            # 确保Python输出不缓冲
    }
    
    # 只设置未在当前环境中存在的变量，避免覆盖用户自定义设置
    return {k: v for k, v in npu_env.items() if k not in os.environ}


def ddp_cleanup(trainer, file):
    """Delete temp file if created."""
    if f"{id(trainer)}.py" in file:  # if temp_file suffix in file
        os.remove(file)
