# 训练模型管理工作流程

## 📋 概述

这个文档描述了完整的训练模型管理工作流程，包括训练过程中的模型信息自动获取、模型导出和推理功能。

## 🔄 完整工作流程

### 1. 训练阶段

#### 1.1 训练脚本执行
```bash
# 在远端服务器执行训练脚本
python utils/train_yolov8_multi.py \
    --config config.json \
    --data datasets/1/data.yaml \
    --task_id 123 \
    --api_base_url http://your-api-server.com \
    --auth_token your_auth_token \
    --auto_save_models
```

#### 1.2 自动模型信息获取
- 训练脚本在每轮次结束后自动扫描 `runs/detect/train/weights/` 目录
- 发现新的 `.pt` 文件时，调用 `model_info_extractor.py` 提取模型信息
- 自动生成模型名称：`task_{任务ID}_{模型文件名}`
- 通过API将模型信息保存到数据库

#### 1.3 模型信息包含
- **基本信息**: 模型路径、文件大小、创建时间
- **性能指标**: 准确率、精度、召回率、适应度分数
- **推理性能**: 推理速度(FPS)、推理时间(毫秒)
- **模型架构**: 类别数量、模型架构类型

### 2. 模型导出阶段

#### 2.1 导出请求
```javascript
// 前端发起导出请求
const exportData = {
    model_id: 123,
    export_format: 'om',
    export_device: 'npu',
    optimization_level: 'O2'
};

fetch('/backend/training/models/123/export', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer your_token',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(exportData)
});
```

#### 2.2 导出执行流程
1. **脚本上传**: `yolov8_docker_trainer.py` 自动上传 `model_info_extractor.py` 到远端服务器
2. **导出执行**: 在远端服务器执行导出命令
3. **状态更新**: 更新模型导出状态和日志
4. **结果保存**: 保存导出后的 `.om` 文件路径和大小信息

#### 2.3 导出命令示例
```bash
# 在远端服务器执行
python model_info_extractor.py \
    --export_model \
    --model_path /path/to/model.pt \
    --output_path /path/to/model.om \
    --device npu \
    --optimization_level O2
```

### 3. 模型推理阶段

#### 3.1 推理请求
```javascript
// 前端发起推理请求
const inferenceData = {
    model_id: 123,
    input_source: '/path/to/test_image.jpg',
    confidence_threshold: 0.5,
    iou_threshold: 0.45,
    save_result: true,
    return_image: true
};

fetch('/backend/training/models/123/inference', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer your_token',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(inferenceData)
});
```

#### 3.2 推理执行流程
1. **脚本上传**: 确保推理脚本已上传到远端服务器
2. **推理执行**: 在远端服务器执行推理命令
3. **结果解析**: 解析推理结果JSON数据
4. **日志保存**: 保存推理日志到数据库

#### 3.3 推理命令示例
```bash
# 在远端服务器执行
python model_info_extractor.py \
    --inference \
    --model_path /path/to/model.pt \
    --input_source /path/to/image.jpg \
    --confidence_threshold 0.5
```

## 🛠️ 核心组件

### 1. train_yolov8_multi.py
- **功能**: 主训练脚本
- **新增功能**: 
  - 自动模型信息获取
  - API集成
  - 数据库保存

### 2. model_info_extractor.py
- **功能**: 模型信息提取、导出、推理工具
- **支持操作**:
  - `--extract_info`: 提取模型信息
  - `--export_model`: 导出模型为OM格式
  - `--inference`: 运行模型推理

### 3. yolov8_docker_trainer.py
- **功能**: Docker训练器
- **新增方法**:
  - `upload_export_script()`: 上传导出脚本
  - `export_model_to_om()`: 导出模型
  - `run_model_inference()`: 运行推理

### 4. backend_api/views/training_model.py
- **功能**: API视图
- **集成**: 调用训练器执行实际的导出和推理操作

## 📊 数据库模型

### TrainingModel
- 存储模型基本信息和性能指标
- 所有字段支持空值，灵活适应不同场景

### ModelExportLog
- 记录模型导出过程和结果
- 包含导出状态、错误信息、日志内容

### ModelInferenceLog
- 记录模型推理历史
- 包含推理结果、性能数据

## 🔧 配置说明

### 训练脚本参数
```bash
python train_yolov8_multi.py \
    --config config.json \              # 训练配置文件
    --data datasets/1/data.yaml \       # 数据集配置
    --task_id 123 \                     # 训练任务ID
    --api_base_url http://api.com \     # API服务器地址
    --auth_token token123 \             # 认证令牌
    --auto_save_models                  # 启用自动保存模型信息
```

### API配置
- 确保API服务器可访问
- 提供有效的认证令牌
- 配置正确的URL路径

## 🚀 使用示例

### 完整训练流程
```bash
# 1. 启动训练并自动保存模型信息
python utils/train_yolov8_multi.py \
    --config training_config.json \
    --data datasets/coco/data.yaml \
    --task_id 456 \
    --api_base_url https://your-api.com \
    --auth_token eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9... \
    --auto_save_models

# 2. 训练完成后，通过API导出模型
curl -X POST https://your-api.com/backend/training/models/123/export \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{"export_format": "om", "export_device": "npu"}'

# 3. 使用模型进行推理
curl -X POST https://your-api.com/backend/training/models/123/inference \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{"input_source": "/path/to/test.jpg", "confidence_threshold": 0.5}'
```

## ⚠️ 注意事项

1. **网络连接**: 确保训练服务器能访问API服务器
2. **权限管理**: 用户只能操作自己创建的模型
3. **文件路径**: 确保模型文件路径在远端服务器上有效
4. **资源管理**: 导出和推理操作会消耗计算资源
5. **错误处理**: 所有操作都有完整的错误处理和日志记录

## 📈 监控和日志

- **训练日志**: 通过 `/backend/training/{id}/logs` 获取
- **导出日志**: 通过 `/backend/training/models/{id}/export-logs` 获取
- **推理日志**: 通过 `/backend/training/models/{id}/inference-logs` 获取
- **实时监控**: 支持SSE实时日志流

这个工作流程实现了从训练到部署的完整自动化管理！
