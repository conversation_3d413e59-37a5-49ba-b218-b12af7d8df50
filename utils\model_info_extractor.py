#!/usr/bin/env python3
import argparse
import os
import sys
import argparse
import subprocess
from pathlib import Path


# 添加当前目录到Python路径
ultralytics_dir = "/root/siton-data-b496463103254f46976c4ff88ea74bc9/data/ultralytics_v8"
sys.path.insert(0, str(ultralytics_dir))

# 设置环境变量
os.environ["PYTHONPATH"] = str(ultralytics_dir)


def convert_model(model_path, chip_name="910B3"):
    """
    将模型转换为ONNX格式，然后转换为OM格式
    
    Args:
        model_path: 模型文件路径
        chip_name: 芯片型号
    
    Returns:
        tuple: (onnx_path, om_path) 转换后的ONNX和OM模型路径
    """
    from ultralytics import YOLO
    
    # 获取模型名称（不带扩展名）
    model_name = os.path.splitext(os.path.basename(model_path))[0]
    
    print(f"开始转换模型: {model_path}")
    
    # 加载模型并导出为ONNX格式
    model = YOLO(model_path)
    onnx_model = model.export(format="onnx", dynamic=True, simplify=True, opset=11)
    print(f"ONNX模型已导出: {onnx_model}")
    
    # 安装必要的pip包
    data_dir = "/root/siton-data-b496463103254f46976c4ff88ea74bc9/data"
    pip_packages = [
        os.path.join(data_dir, "aclruntime-0.0.2-cp39-cp39-linux_aarch64.whl"),
        os.path.join(data_dir, "ais_bench-0.0.2-py3-none-any.whl")
    ]
    
    for package in pip_packages:
        try:
            print(f"正在安装: {package}")
            result = subprocess.run(f"pip install {package}", shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            print(f"安装成功: {package}")
        except subprocess.CalledProcessError as e:
            print(f"安装失败: {package}")
            print(f"错误信息: {e.stderr}")
            return None, None  # 如果安装失败，返回None
    
    # 设置参数
    batchsize = 1
    
    # 构建输出OM模型路径
    om_model = f"{model_name}_bs{batchsize}.om"
    
    # 获取onnx_model的上一层目录
    parent_dir = os.path.dirname(os.path.dirname(onnx_model))
    output_name = f"{os.path.join(parent_dir, model_name)}_bs{batchsize}"
    
    # 构建atc命令
    atc_cmd = f"atc --framework=5 --model={onnx_model} --input_format=NCHW --input_shape=\"images:{batchsize},3,640,640\" --output_type=FP16 --output={output_name} --log=error --soc_version=Ascend{chip_name}"
    
    print(f"执行ATC命令: {atc_cmd}")
    
    # 设置环境变量
    env_setup = 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH'
    
    # 执行atc命令
    try:
        # 使用bash -c来确保在正确的shell环境中执行，并设置环境变量
        bash_cmd = f"bash -c '{env_setup} && {atc_cmd}'"
        print(f"完整命令: {bash_cmd}")
        result = subprocess.run(bash_cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 更新om_model为完整路径
        om_model_path = f"{output_name}.om"
        print(f"ATC转换成功: {om_model_path}")
        print(result.stdout)
        return onnx_model, om_model_path
    except subprocess.CalledProcessError as e:
        print(f"ATC转换失败: {e}")
        print(f"错误信息: {e.stderr}")
        return onnx_model, None


def infer_model(model_path, image_path, save_path=None, conf_thres=0.25):
    """
    使用YOLO模型进行推理
    
    Args:
        model_path: 模型文件路径
        image_path: 输入图片路径
        save_path: 结果保存路径，默认为None（自动生成）
        conf_thres: 置信度阈值
        
    Returns:
        str: 结果图片保存路径
    """
    from ultralytics import YOLO
    
    print(f"加载模型: {model_path}")
    model = YOLO(model_path)
    
    print(f"对图片进行推理: {image_path}")
    results = model.predict(
        source=image_path,
        conf=conf_thres,
        save=True,
        save_txt=True,
        save_conf=True,
        project="results",
        name=os.path.splitext(os.path.basename(image_path))[0]
    )
    
    # 获取结果图片路径
    result_path = results[0].save_dir
    result_img = os.path.join(result_path, os.path.basename(image_path))
    
    # 如果指定了保存路径，则复制结果
    if save_path:
        import shutil
        shutil.copy(result_img, save_path)
        result_img = save_path
    
    print(f"推理完成，结果保存在: {result_img}")
    
    # 打印检测结果
    for r in results:
        boxes = r.boxes
        print(f"检测到 {len(boxes)} 个目标")
        for i, box in enumerate(boxes):
            cls = int(box.cls[0])
            conf = float(box.conf[0])
            name = model.names[cls]
            print(f"目标 {i+1}: 类别={name}, 置信度={conf:.2f}, 坐标={box.xyxy[0].tolist()}")
    
    return result_img


def main():
    parser = argparse.ArgumentParser(description="YOLOv8模型转换与推理工具")
    subparsers = parser.add_subparsers(dest="command", help="选择命令")
    
    # 转换模型命令
    convert_parser = subparsers.add_parser("convert", help="转换模型格式")
    convert_parser.add_argument('--pt', required=True, help='输入模型路径(.pt文件)')
    convert_parser.add_argument('--chip_name', default="910B3", help='芯片型号')
    
    # 推理命令
    infer_parser = subparsers.add_parser("infer", help="使用模型进行推理")
    infer_parser.add_argument('--model', required=True, help='模型路径(.pt, .onnx或其他支持的格式)')
    infer_parser.add_argument('--image', required=True, help='输入图片路径')
    infer_parser.add_argument('--save_path', help='结果保存路径')
    infer_parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    
    args = parser.parse_args()
    
    if args.command == "convert":
        convert_model(args.pt, args.chip_name)
    elif args.command == "infer":
        infer_model(args.model, args.image, args.save_path, args.conf)
    else:
        parser.print_help()


if __name__ == '__main__':
    main()