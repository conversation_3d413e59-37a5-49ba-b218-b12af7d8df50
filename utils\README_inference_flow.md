# 模型推理数据流说明

## 概述

本文档说明了模型推理功能中base64数据和文件路径的处理流程。

## 数据流架构

```
API请求 -> model_converter.py -> model_info_extractor.py -> YOLO模型
```

## 详细流程

### 1. API层 (backend_api)

- 接收用户请求，包含 `input_source` 字段
- `input_source` 可以是：
  - 文件路径：`/path/to/image.jpg`
  - URL：`http://example.com/image.jpg`
  - Base64数据：`data:image/jpeg;base64,/9j/4AAQ...`

### 2. model_converter.py

**职责：**
- 处理base64数据转换
- 管理远程服务器连接
- 协调推理流程

**Base64处理逻辑：**
```python
# 检测base64数据
if len(input_source) > 200 and not input_source.startswith('/') and not input_source.startswith('http'):
    # 1. 解码base64数据
    # 2. 通过SFTP直接写入远程服务器
    # 3. 生成远程临时文件路径
    # 4. 传递文件路径给model_info_extractor.py
```

**优势：**
- 避免了 "Argument list too long" 错误
- 减少了本地临时文件的创建
- 直接在远程服务器上处理数据

### 3. model_info_extractor.py

**职责：**
- 接收文件路径（不再处理base64）
- 调用YOLO模型进行推理
- 返回推理结果

**简化后的逻辑：**
```python
def process_input_source(input_source):
    # 现在只处理文件路径
    # base64数据已在上游处理完成
    return input_source, False
```

## 错误解决

### 问题：`/bin/bash: Argument list too long`

**原因：** 直接将长base64字符串作为命令行参数传递

**解决方案：**
1. 在 `model_converter.py` 中检测base64数据
2. 将base64数据解码并写入远程临时文件
3. 传递文件路径而不是base64数据给推理脚本

### 问题：文件路径识别错误

**原因：** `model_info_extractor.py` 错误地将远程文件路径识别为base64数据

**解决方案：**
1. 简化 `process_input_source` 函数
2. 移除base64检测逻辑
3. 只处理文件路径

## 临时文件管理

### 创建
- 在 `model_converter.py` 中创建远程临时文件
- 文件名格式：`temp_input_{timestamp}.jpg`

### 清理
- 推理完成后自动清理
- 异常情况下也会尝试清理
- 清理命令：`rm -f {remote_temp_path}`

## 测试

使用 `test_base64_processing.py` 测试文件路径处理：

```bash
cd utils
python test_base64_processing.py
```

## 配置要求

### 数据库字段
```python
# 修改前
input_source = models.CharField(max_length=500, ...)

# 修改后  
input_source = models.TextField(...)
```

### 依赖包
- paramiko (SSH/SFTP连接)
- ultralytics (YOLO模型)

## 性能优化

1. **减少文件传输**：直接在远程服务器写入数据
2. **避免本地临时文件**：使用SFTP直接写入
3. **及时清理**：推理完成后立即删除临时文件
4. **错误处理**：异常情况下也确保清理临时文件

## 注意事项

1. 确保远程服务器有足够的磁盘空间存储临时文件
2. 网络连接稳定性影响base64数据传输
3. 大文件传输可能需要增加超时时间
4. 并发请求时注意临时文件名冲突（使用时间戳避免）
