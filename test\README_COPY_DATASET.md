# 随机数据集复制工具

## 📋 功能说明

这个脚本用于从 `datasets/1` 目录中随机选择指定数量的图片及其对应的标签文件，复制到 `datasets/4` 目录中，保持原有的目录结构。默认从训练集选择100张，从验证集选择10张。

## 🗂️ 目录结构

### 源目录结构 (datasets/1)
```
datasets/1/
├── images/
│   ├── train/
│   │   ├── img1.jpg
│   │   ├── img2.jpg
│   │   └── ...
│   └── val/
│       ├── img100.jpg
│       ├── img101.jpg
│       └── ...
└── labels/
    ├── train/
    │   ├── img1.txt
    │   ├── img2.txt
    │   └── ...
    └── val/
        ├── img100.txt
        ├── img101.txt
        └── ...
```

### 目标目录结构 (datasets/4)
```
datasets/4/
├── images/
│   ├── train/          # 随机选择的训练图片
│   └── val/            # 随机选择的验证图片
└── labels/
    ├── train/          # 对应的训练标签
    └── val/            # 对应的验证标签
```

## 🚀 使用方法

### 基本用法
```bash
# 默认从datasets/1复制训练集100张、验证集10张图片到datasets/4
python test/copy_random_dataset.py
```

### 自定义参数
```bash
# 指定源目录、目标目录和样本数量
python test/copy_random_dataset.py \
    --source datasets/1 \
    --target datasets/4 \
    --train_samples 200 \
    --val_samples 20 \
    --seed 42
```

### 参数说明
- `--source`: 源数据集目录 (默认: `datasets/1`)
- `--target`: 目标数据集目录 (默认: `datasets/4`)
- `--train_samples`: 训练集要复制的样本数量 (默认: `100`)
- `--val_samples`: 验证集要复制的样本数量 (默认: `10`)
- `--seed`: 随机种子，确保结果可重现 (默认: `42`)

## 📊 功能特点

### 1. 智能匹配
- 只复制有对应标签文件的图片
- 自动匹配图片和标签文件名
- 支持多种图片格式：`.jpg`, `.jpeg`, `.png`, `.bmp`, `.tiff`, `.tif`

### 2. 随机采样
- 从train和val目录中随机选择样本
- 保持原有的train/val分布比例
- 使用随机种子确保结果可重现

### 3. 目录管理
- 自动创建目标目录结构
- 保持与源目录相同的组织方式
- 安全的文件复制操作

### 4. 详细反馈
- 显示找到的图片和标签数量
- 实时显示复制进度
- 统计成功和失败的操作

## 📈 使用示例

### 示例1：默认复制（训练集100张，验证集10张）
```bash
python test/copy_random_dataset.py
```

输出：
```
🚀 随机数据集复制工具
==================================================
📂 源目录: datasets/1
📂 目标目录: datasets/4
🎯 训练集样本数量: 100
🎯 验证集样本数量: 10
🎲 随机种子: 42
==================================================
📁 创建目录: datasets/4/images/train
📁 创建目录: datasets/4/images/val
📁 创建目录: datasets/4/labels/train
📁 创建目录: datasets/4/labels/val
📊 train目录找到 800 张图片，其中 800 张有对应标签
📊 val目录找到 200 张图片，其中 200 张有对应标签
📊 总共找到 1000 个有效的图片-标签对
🎯 随机选择了 110 个样本进行复制
📈 选择分布: train=100, val=10
📋 已复制 20/110 个样本...
📋 已复制 40/110 个样本...
📋 已复制 60/110 个样本...
📋 已复制 80/110 个样本...
📋 已复制 100/110 个样本...

✅ 复制完成!
📊 成功复制: 110 个样本
❌ 失败: 0 个样本
📁 目标目录: datasets/4

🎉 任务完成!
```

### 示例2：复制更多样本
```bash
python test/copy_random_dataset.py --train_samples 500 --val_samples 50 --seed 123
```

### 示例3：自定义目录
```bash
python test/copy_random_dataset.py \
    --source datasets/original \
    --target datasets/subset \
    --train_samples 50 \
    --val_samples 5
```

## ⚠️ 注意事项

### 1. 目录要求
- 源目录必须存在且包含正确的目录结构
- 图片和标签文件名必须匹配（除了扩展名）
- 标签文件必须是 `.txt` 格式

### 2. 文件匹配
- 图片文件：`image.jpg` → 标签文件：`image.txt`
- 只有同时存在图片和标签的样本才会被复制
- 不匹配的文件会被自动跳过

### 3. 空间要求
- 确保目标目录有足够的磁盘空间
- 脚本会覆盖目标目录中的同名文件

## 🔧 故障排除

### 常见问题

1. **源目录不存在**
   ```
   ❌ 源目录不存在: datasets/1
   ```
   解决：检查源目录路径是否正确

2. **没有找到有效样本**
   ```
   ❌ 没有找到任何有效的图片-标签对
   ```
   解决：检查目录结构和文件匹配

3. **样本数量不足**
   ```
   ⚠️  可用样本数 (50) 少于请求数量 (100)，将复制所有可用样本
   ```
   解决：减少请求的样本数量或检查源数据

4. **复制失败**
   ```
   ❌ 复制失败 image.jpg: [Errno 13] Permission denied
   ```
   解决：检查文件权限和磁盘空间

## 🎯 高级用法

### 批量处理多个数据集
```bash
# 创建多个子集
for i in {1..5}; do
    python test/copy_random_dataset.py \
        --target datasets/subset_$i \
        --train_samples 100 \
        --val_samples 10 \
        --seed $i
done
```

### 按比例分割
```bash
# 创建小型测试集
python test/copy_random_dataset.py \
    --train_samples 50 \
    --val_samples 5 \
    --target datasets/test

# 创建中型验证集
python test/copy_random_dataset.py \
    --train_samples 200 \
    --val_samples 20 \
    --target datasets/validation
```

这个工具可以帮助你快速创建数据集的子集，用于模型测试、验证或演示。
