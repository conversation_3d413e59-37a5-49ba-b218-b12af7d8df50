#!/usr/bin/env python3
"""
测试om_infer.py的环境设置和导入
验证本地ultralytics源码是否正确加载
"""

import sys
import os
from pathlib import Path

def test_ultralytics_path_setup():
    """测试ultralytics路径设置"""
    print("🔧 测试ultralytics路径设置")
    print("=" * 50)
    
    # 模拟om_infer.py中的路径设置
    current_dir = Path(__file__).parent.absolute()
    ultralytics_dir = current_dir.parent / "data" / "ultralytics_v8"
    
    print(f"当前目录: {current_dir}")
    print(f"ultralytics目录: {ultralytics_dir}")
    print(f"ultralytics目录存在: {ultralytics_dir.exists()}")
    
    if ultralytics_dir.exists():
        ultralytics_package = ultralytics_dir / "ultralytics"
        print(f"ultralytics包目录: {ultralytics_package}")
        print(f"ultralytics包存在: {ultralytics_package.exists()}")
        
        if ultralytics_package.exists():
            init_file = ultralytics_package / "__init__.py"
            print(f"__init__.py存在: {init_file.exists()}")
            
            # 检查关键模块
            key_modules = [
                "nn/tasks.py",
                "yolo/cfg.py", 
                "yolo/utils/__init__.py",
                "yolo/utils/metrics.py",
                "yolo/utils/ops.py",
                "yolo/utils/plotting.py",
                "yolo/data/__init__.py"
            ]
            
            print("\n关键模块检查:")
            for module in key_modules:
                module_path = ultralytics_package / module
                status = "✅" if module_path.exists() else "❌"
                print(f"  {status} {module}")
        else:
            print("❌ ultralytics包目录不存在")
    else:
        print("❌ ultralytics_v8目录不存在")

def test_import_with_path():
    """测试添加路径后的导入"""
    print("\n🔍 测试导入功能")
    print("=" * 50)
    
    # 添加路径
    current_dir = Path(__file__).parent.absolute()
    ultralytics_dir = current_dir.parent / "data" / "ultralytics_v8"
    
    if ultralytics_dir.exists():
        sys.path.insert(0, str(ultralytics_dir))
        os.environ["PYTHONPATH"] = str(ultralytics_dir)
        
        try:
            import ultralytics
            print(f"✅ ultralytics导入成功")
            print(f"   路径: {ultralytics.__file__}")
            
            # 测试关键模块导入
            test_imports = [
                ("ultralytics.nn.tasks", "attempt_load_one_weight"),
                ("ultralytics.yolo.cfg", "get_cfg"),
                ("ultralytics.yolo.utils", "DEFAULT_CFG"),
                ("ultralytics.yolo.utils.metrics", "DetMetrics"),
                ("ultralytics.yolo.utils.ops", "Profile"),
            ]
            
            print("\n关键模块导入测试:")
            for module_name, item_name in test_imports:
                try:
                    module = __import__(module_name, fromlist=[item_name])
                    getattr(module, item_name)
                    print(f"  ✅ {module_name}.{item_name}")
                except Exception as e:
                    print(f"  ❌ {module_name}.{item_name}: {e}")
                    
        except ImportError as e:
            print(f"❌ ultralytics导入失败: {e}")
    else:
        print("❌ 无法测试导入，ultralytics_v8目录不存在")

def test_ais_bench_availability():
    """测试ais_bench可用性"""
    print("\n🚀 测试ais_bench可用性")
    print("=" * 50)
    
    try:
        from ais_bench.infer.interface import InferSession
        print("✅ ais_bench可用")
        print("   可以进行OM模型推理")
    except ImportError as e:
        print("⚠️  ais_bench不可用")
        print(f"   错误: {e}")
        print("   💡 在华为昇腾环境中安装: pip install ais_bench")

def test_other_dependencies():
    """测试其他依赖"""
    print("\n📦 测试其他依赖")
    print("=" * 50)
    
    dependencies = [
        ("torch", "PyTorch"),
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("tqdm", "tqdm"),
        ("pathlib", "pathlib")
    ]
    
    for module_name, display_name in dependencies:
        try:
            __import__(module_name)
            print(f"✅ {display_name}")
        except ImportError:
            print(f"❌ {display_name} - 需要安装")

def main():
    """主测试函数"""
    print("🧪 OM推理脚本环境测试")
    print("=" * 60)
    
    test_ultralytics_path_setup()
    test_import_with_path()
    test_ais_bench_availability()
    test_other_dependencies()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")
    print("\n💡 使用建议:")
    print("1. 确保data/ultralytics_v8目录存在且包含完整源码")
    print("2. 在华为昇腾环境中安装ais_bench")
    print("3. 运行: python test/om_infer.py --help 查看使用方法")
    print("4. 脚本会自动使用data/ultralytics_v8目录下的本地源码")

if __name__ == "__main__":
    main()
