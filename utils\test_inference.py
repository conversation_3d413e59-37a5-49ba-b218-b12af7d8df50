#!/usr/bin/env python3
"""
推理功能测试脚本
用于测试模型推理功能和调试输出解析问题
"""

import os
import sys
import logging
from model_converter import ModelConverter

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_inference():
    """测试推理功能"""
    
    # 服务器连接信息（请根据实际情况修改）
    server_info = {
        'ip': '*************',  # 替换为实际IP
        'port': 22,
        'password': 'your_password',  # 替换为实际密码
        'username': 'root'
    }
    
    # 测试参数
    model_path = '/path/to/your/model.pt'  # 替换为实际模型路径
    test_image = '/path/to/test/image.jpg'  # 替换为实际测试图片路径
    
    try:
        logger.info("开始测试推理功能...")
        
        # 创建模型转换器
        converter = ModelConverter(server_info)
        
        # 执行推理
        result = converter.run_inference(
            model_path=model_path,
            input_source=test_image,
            confidence_threshold=0.5,
            output_path="/workspace/test_result.jpg"
        )
        
        # 输出结果
        logger.info("推理结果:")
        logger.info(f"成功: {result.get('success', False)}")
        logger.info(f"检测数量: {result.get('detection_count', 0)}")
        logger.info(f"结果路径: {result.get('result_path', 'N/A')}")
        logger.info(f"图片数据大小: {len(result.get('image_data', '')) / 1024:.2f} KB" if result.get('image_data') else "无图片数据")
        
        if result.get('detections'):
            logger.info("检测详情:")
            for i, det in enumerate(result['detections']):
                logger.info(f"  目标 {i+1}: {det['class']} (置信度: {det['confidence']:.2f})")
        
        # 输出完整的推理日志用于调试
        if result.get('inference_log'):
            logger.info("完整推理日志:")
            logger.info(result['inference_log'])
        
        # 关闭连接
        converter.disconnect()
        
        return result
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def test_base64_inference():
    """测试base64输入的推理功能"""
    
    # 创建一个简单的base64测试图片（1x1像素的PNG）
    test_base64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    server_info = {
        'ip': '*************',  # 替换为实际IP
        'port': 22,
        'password': 'your_password',  # 替换为实际密码
        'username': 'root'
    }
    
    model_path = '/path/to/your/model.pt'  # 替换为实际模型路径
    
    try:
        logger.info("开始测试base64推理功能...")
        
        converter = ModelConverter(server_info)
        
        result = converter.run_inference(
            model_path=model_path,
            input_source=test_base64,
            confidence_threshold=0.5
        )
        
        logger.info("Base64推理结果:")
        logger.info(f"成功: {result.get('success', False)}")
        logger.info(f"检测数量: {result.get('detection_count', 0)}")
        
        converter.disconnect()
        
        return result
        
    except Exception as e:
        logger.error(f"Base64测试失败: {e}")
        return None

if __name__ == "__main__":
    print("推理功能测试")
    print("1. 测试文件路径推理")
    print("2. 测试base64推理")
    print("3. 退出")
    
    choice = input("请选择测试类型 (1-3): ")
    
    if choice == "1":
        test_inference()
    elif choice == "2":
        test_base64_inference()
    elif choice == "3":
        print("退出测试")
    else:
        print("无效选择")
