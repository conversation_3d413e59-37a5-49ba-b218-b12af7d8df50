#!/usr/bin/env python3
"""
测试base64数据处理的脚本
"""

import os
import sys
import base64
import tempfile

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from model_info_extractor import process_input_source

def test_file_path():
    """测试文件路径处理"""
    print("=== 测试文件路径处理 ===")
    
    # 测试绝对路径
    test_path = "/workspace/test_image.jpg"
    try:
        result_path, is_temp = process_input_source(test_path)
        print(f"输入: {test_path}")
        print(f"输出: {result_path}")
        print(f"是否临时文件: {is_temp}")
        print("✅ 文件路径处理成功")
    except Exception as e:
        print(f"❌ 文件路径处理失败: {e}")
    print()

def test_remote_file_path():
    """测试远程文件路径处理"""
    print("=== 测试远程文件路径处理 ===")

    # 测试远程文件路径（这种路径在本地不存在，但在远程服务器上存在）
    remote_path = "/workspace/temp_input_12345.jpg"

    try:
        result_path, is_temp = process_input_source(remote_path)
        print(f"输入: {remote_path}")
        print(f"输出: {result_path}")
        print(f"是否临时文件: {is_temp}")
        print("✅ 远程文件路径处理成功")
    except Exception as e:
        print(f"❌ 远程文件路径处理失败: {e}")
    print()

def test_url():
    """测试URL处理"""
    print("=== 测试URL处理 ===")
    
    test_url = "http://example.com/image.jpg"
    try:
        result_path, is_temp = process_input_source(test_url)
        print(f"输入: {test_url}")
        print(f"输出: {result_path}")
        print(f"是否临时文件: {is_temp}")
        print("✅ URL处理成功")
    except Exception as e:
        print(f"❌ URL处理失败: {e}")
    print()

def test_edge_cases():
    """测试边界情况"""
    print("=== 测试边界情况 ===")

    # 测试相对路径
    relative_path = "images/test.jpg"
    try:
        result_path, is_temp = process_input_source(relative_path)
        print(f"相对路径 '{relative_path}': {result_path}, 临时文件: {is_temp}")
    except Exception as e:
        print(f"相对路径处理失败: {e}")

    # 测试空字符串
    try:
        result_path, is_temp = process_input_source("")
        print(f"空字符串: {result_path}, 临时文件: {is_temp}")
    except Exception as e:
        print(f"空字符串处理失败: {e}")

    print()

if __name__ == "__main__":
    print("文件路径处理测试")
    print("=" * 50)

    test_file_path()
    test_remote_file_path()
    test_url()
    test_edge_cases()

    print("测试完成!")
