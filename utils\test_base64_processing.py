#!/usr/bin/env python3
"""
测试base64数据处理的脚本
"""

import os
import sys
import base64
import tempfile

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from model_info_extractor import process_input_source

def test_file_path():
    """测试文件路径处理"""
    print("=== 测试文件路径处理 ===")
    
    # 测试绝对路径
    test_path = "/workspace/test_image.jpg"
    try:
        result_path, is_temp = process_input_source(test_path)
        print(f"输入: {test_path}")
        print(f"输出: {result_path}")
        print(f"是否临时文件: {is_temp}")
        print("✅ 文件路径处理成功")
    except Exception as e:
        print(f"❌ 文件路径处理失败: {e}")
    print()

def test_base64_data():
    """测试base64数据处理"""
    print("=== 测试base64数据处理 ===")
    
    # 创建一个简单的1x1像素PNG图片的base64数据
    test_base64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    try:
        result_path, is_temp = process_input_source(test_base64)
        print(f"输入: {test_base64[:50]}...")
        print(f"输出: {result_path}")
        print(f"是否临时文件: {is_temp}")
        
        # 检查文件是否真的被创建
        if os.path.exists(result_path):
            file_size = os.path.getsize(result_path)
            print(f"临时文件大小: {file_size} 字节")
            print("✅ Base64数据处理成功")
            
            # 清理临时文件
            if is_temp:
                os.unlink(result_path)
                print("🗑️ 临时文件已清理")
        else:
            print("❌ 临时文件未创建")
            
    except Exception as e:
        print(f"❌ Base64数据处理失败: {e}")
        import traceback
        traceback.print_exc()
    print()

def test_pure_base64():
    """测试纯base64数据（无前缀）"""
    print("=== 测试纯base64数据处理 ===")
    
    # 纯base64数据（无data:image/前缀）
    pure_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    try:
        result_path, is_temp = process_input_source(pure_base64)
        print(f"输入: {pure_base64[:50]}...")
        print(f"输出: {result_path}")
        print(f"是否临时文件: {is_temp}")
        
        # 检查文件是否真的被创建
        if os.path.exists(result_path):
            file_size = os.path.getsize(result_path)
            print(f"临时文件大小: {file_size} 字节")
            print("✅ 纯Base64数据处理成功")
            
            # 清理临时文件
            if is_temp:
                os.unlink(result_path)
                print("🗑️ 临时文件已清理")
        else:
            print("❌ 临时文件未创建")
            
    except Exception as e:
        print(f"❌ 纯Base64数据处理失败: {e}")
        import traceback
        traceback.print_exc()
    print()

def test_url():
    """测试URL处理"""
    print("=== 测试URL处理 ===")
    
    test_url = "http://example.com/image.jpg"
    try:
        result_path, is_temp = process_input_source(test_url)
        print(f"输入: {test_url}")
        print(f"输出: {result_path}")
        print(f"是否临时文件: {is_temp}")
        print("✅ URL处理成功")
    except Exception as e:
        print(f"❌ URL处理失败: {e}")
    print()

def test_edge_cases():
    """测试边界情况"""
    print("=== 测试边界情况 ===")
    
    # 测试短字符串
    short_string = "short"
    try:
        result_path, is_temp = process_input_source(short_string)
        print(f"短字符串 '{short_string}': {result_path}, 临时文件: {is_temp}")
    except Exception as e:
        print(f"短字符串处理失败: {e}")
    
    # 测试包含文件扩展名的长字符串
    long_with_ext = "a" * 150 + ".jpg"
    try:
        result_path, is_temp = process_input_source(long_with_ext)
        print(f"长字符串带扩展名: 临时文件: {is_temp}")
    except Exception as e:
        print(f"长字符串带扩展名处理失败: {e}")
    
    print()

if __name__ == "__main__":
    print("Base64数据处理测试")
    print("=" * 50)
    
    test_file_path()
    test_base64_data()
    test_pure_base64()
    test_url()
    test_edge_cases()
    
    print("测试完成!")
